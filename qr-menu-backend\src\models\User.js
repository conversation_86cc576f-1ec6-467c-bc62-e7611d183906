const { DataTypes, Model } = require('sequelize');
const sequelize = require('../config/database'); // DB bağlantısını import edirik
// bcrypt artıq istifadə olunmur

class User extends Model {
  // Parolu yoxlamaq üçün instance metodu (artıq istifadə olunmur)
  // async isValidPassword(password) {
  //   return bcrypt.compare(password, this.password_hash);
  // }
}

User.init({
  user_id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4, // Avtomatik UUID generasiyası
    primaryKey: true,
    allowNull: false,
  },
  username: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: { msg: "İstifadəçi adı boş ola bilməz." },
      len: { args: [3, 50], msg: "İstifadəçi adı 3-50 simvol arasında olmalıdır."}
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: { msg: "Düzgün email formatı daxil edin." },
      notEmpty: { msg: "Email boş ola bilməz." }
    }
  },
  password_hash: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
        notEmpty: { msg: "Parol boş ola bilməz."}
    }
  },
  full_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
        notEmpty: { msg: "Tam ad boş ola bilməz."}
    }
  },
  role: {
    type: DataTypes.ENUM('admin', 'waiter'),
    allowNull: false,
    defaultValue: 'waiter',
    validate: {
        isIn: { args: [['admin', 'waiter']], msg: "Rol 'admin' və ya 'waiter' olmalıdır."}
    }
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  // created_at və updated_at Sequelize tərəfindən avtomatik idarə olunur (timestamps: true ilə)
}, {
  sequelize,
  modelName: 'User', // Modelin adı
  tableName: 'users', // Verilənlər bazasındakı cədvəlin adı
  timestamps: true, // created_at və updated_at sütunlarını avtomatik əlavə edir
  underscored: true, // Sütun adlarını snake_case formatında (məsələn, user_id) saxlayır

  // hooks artıq istifadə olunmur - hash-ləmə auth route-da edilir
});

module.exports = User;
