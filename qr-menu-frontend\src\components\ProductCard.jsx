import React from 'react';
import { useCart } from '../context/CartContext';

const ProductCard = ({ product }) => {
  const { addToCart } = useCart();

  // Placeholder image if no image is provided
  const imageUrl = product.image_url || 'https://via.placeholder.com/300';

  return (
    <div className="card bg-light rounded-lg shadow-lg overflow-hidden transform transition-transform hover:scale-105 duration-300">
      <img src={imageUrl} alt={product.name} className="w-full h-48 object-cover" />
      <div className="p-4">
        <h3 className="text-xl font-bold mb-2">{product.name}</h3>
        <p className="text-gray-600 mb-4 h-16 overflow-hidden">{product.description}</p>
        <div className="flex justify-between items-center">
          <span className="text-2xl font-bold text-secondary">{product.price} ₼</span>
          <button
            onClick={() => addToCart(product)}
            className="bg-primary text-white font-bold py-2 px-4 rounded-lg hover:bg-primary/80 transition-colors"
          >
            Səbətə at
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
