const jwt = require('jsonwebtoken');
const db = require('../models');
const User = db.User;

// JWT token-ini yoxlayan middleware
exports.authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ message: 'Giriş token-i tələb olunur.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.user_id);
    
    if (!user) {
      return res.status(401).json({ message: 'Etibarsız token.' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Token yoxlama xətası:', error);
    return res.status(403).json({ message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> və ya vaxtı keçmiş token.' });
  }
};

// Admin rolunu yoxlayan middleware
exports.isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    return res.status(403).json({ message: 'Bu əməliyyat üçün admin hüquqları tələb olunur.' });
  }
};

// Waiter rolunu yoxlayan middleware
exports.isWaiter = (req, res, next) => {
  if (req.user && (req.user.role === 'waiter' || req.user.role === 'admin')) {
    next();
  } else {
    return res.status(403).json({ message: 'Bu əməliyyat üçün ofisiant hüquqları tələb olunur.' });
  }
};
