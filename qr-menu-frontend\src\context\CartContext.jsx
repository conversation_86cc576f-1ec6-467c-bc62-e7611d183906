import React, { createContext, useState, useContext } from 'react';

// 1. Context-i yaratmaq
const CartContext = createContext();

// 2. Context Provider komponentini yaratmaq
export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);

  // Səbətə məhsul əlavə etmək üçün funksiya
  const addToCart = (product) => {
    setCartItems((prevItems) => {
      const itemExists = prevItems.find((item) => item.product_id === product.product_id);
      if (itemExists) {
        // Əgər məhsul artıq səbətdə varsa, sayını artır
        return prevItems.map((item) =>
          item.product_id === product.product_id ? { ...item, quantity: item.quantity + 1 } : item
        );
      }
      // Əgər məhsul səbətdə yoxdursa, yeni məhsul kimi əlavə et
      return [...prevItems, { ...product, quantity: 1 }];
    });
  };

  // Səbətdən məhsul silmək üçün funksiya
  const removeFromCart = (productId) => {
    setCartItems((prevItems) =>
      prevItems.reduce((acc, item) => {
        if (item.product_id === productId) {
          if (item.quantity > 1) {
            // Məhsulun sayını azalt
            acc.push({ ...item, quantity: item.quantity - 1 });
          }
          // Əgər məhsuldan 1 dənədirsə, tamamilə sil
        } else {
          acc.push(item);
        }
        return acc;
      }, [])
    );
  };

  // Səbəti tamamilə təmizləmək
  const clearCart = () => {
    setCartItems([]);
  };

  // Səbətdəki ümumi məhsul sayı
  const cartItemCount = cartItems.reduce((count, item) => count + item.quantity, 0);

  // Səbətdəki ümumi qiymət
  const cartTotal = cartItems.reduce((total, item) => total + (parseFloat(item.price) * item.quantity), 0);

  return (
    <CartContext.Provider
      value={{
        cartItems,
        addToCart,
        removeFromCart,
        clearCart,
        cartItemCount,
        cartTotal,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

// 3. Context-i istifadə etmək üçün custom hook
export const useCart = () => {
  return useContext(CartContext);
};
