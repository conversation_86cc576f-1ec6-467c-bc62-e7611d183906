import React from 'react';
import { useCart } from '../context/CartContext';
import { createOrder } from '../services/api';
import { useParams, useNavigate } from 'react-router-dom';

const CartPage = () => {
  const { cartItems, addToCart, removeFromCart, clearCart, cartTotal } = useCart();
  const { tableNumber } = useParams();
  const navigate = useNavigate();

  const calculateTotal = () => {
    return cartTotal.toFixed(2);
  };

  const handleOrderSubmit = async () => {
    if (cartItems.length === 0) {
      alert('Səbətiniz boşdur!');
      return;
    }

    if (!tableNumber) {
        alert('Masa nömrəsi təyin edilməyib. Zəhmət olmasa QR kodu yenidən skan edin.');
        return;
    }

    const orderData = {
      table_id: tableNumber,
      items: cartItems.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        price: item.price,
      })),
      total_amount: calculateTotal(),
    };

    try {
      await createOrder(orderData);
      alert('Sifarişiniz uğurla qəbul olundu!');
      clearCart();
      navigate('/order-success');
    } catch (error) {
      console.error('Sifariş göndərilərkən xəta baş verdi:', error);
      alert('Sifariş göndərilərkən xəta baş verdi. Zəhmət olmasa yenidən cəhd edin.');
    }
  };


  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold text-center my-8 text-primary">Səbətiniz</h1>
      {cartItems.length === 0 ? (
        <div className="text-center">
          <p className="text-gray-600 text-xl">Səbətiniz boşdur.</p>
        </div>
      ) : (
        <div className="max-w-2xl mx-auto">
          {cartItems.map((item) => (
            <div key={item.product_id} className="flex items-center justify-between bg-light p-4 rounded-lg shadow-md mb-4">
              <div className="flex items-center space-x-4">
                <img src={item.image_url || 'https://via.placeholder.com/100'} alt={item.name} className="w-20 h-20 object-cover rounded-lg" />
                <div>
                  <h2 className="text-xl font-semibold">{item.name}</h2>
                  <p className="text-secondary font-bold">{item.price} ₼</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <button onClick={() => removeFromCart(item.product_id)} className="bg-gray-200 w-8 h-8 rounded-full font-bold text-lg">-</button>
                  <span className="text-xl font-bold">{item.quantity}</span>
                  <button onClick={() => addToCart(item)} className="bg-gray-200 w-8 h-8 rounded-full font-bold text-lg">+</button>
                </div>
                <p className="text-xl font-bold">{(item.price * item.quantity).toFixed(2)} ₼</p>
              </div>
            </div>
          ))}
          <div className="mt-8 text-right">
            <h2 className="text-2xl font-bold">Cəmi: <span className="text-secondary">{calculateTotal()} ₼</span></h2>
            <button 
              onClick={handleOrderSubmit}
              className="bg-secondary text-white font-bold py-3 px-6 rounded-lg mt-4 hover:bg-secondary/90 transition-colors"
            >
              Sifarişi Tamamla
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CartPage;
