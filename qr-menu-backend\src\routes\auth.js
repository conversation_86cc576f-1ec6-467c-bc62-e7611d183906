const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { User } = require('../models');

const router = express.Router();

// İstifadəçi qeydiyyatı
router.post('/register', async (req, res) => {
  try {

    const { username, email, password, fullName, role } = req.body;

    // İstifadəçi adı və email-in unikal olub olmadığını yoxla
    const existingUser = await User.findOne({
      where: {
        [require('sequelize').Op.or]: [
          { username },
          { email }
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Bu istifadəçi adı və ya email artıq mövcuddur'
      });
    }

    // Müvəqqəti olaraq şifrəni plain text saxlayırıq (test üçün)
    const passwordHash = password;

    // İstifadəçi yarat
    const user = await User.create({
      username,
      email,
      password_hash: passwordHash,
      full_name: fullName,
      role
    });

    // Şifrəni response-dan çıxar
    const userResponse = {
      userId: user.user_id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role,
      isActive: user.is_active,
      createdAt: user.createdAt
    };

    res.status(201).json({
      success: true,
      message: 'İstifadəçi uğurla yaradıldı.',
      user: userResponse
    });

  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({
      success: false,
      message: 'Server xətası'
    });
  }
});

// İstifadəçi girişi
router.post('/login', async (req, res) => {
  try {

    const { username, password } = req.body;

    // İstifadəçini tap
    const user = await User.findOne({
      where: { username }
    });



    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'İstifadəçi adı və ya şifrə yanlışdır'
      });
    }

    // İstifadəçinin aktiv olub olmadığını yoxla
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Hesab deaktivdir'
      });
    }

    // Şifrəni yoxla (müvəqqəti plain text)
    const isPasswordValid = password === user.password_hash;
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'İstifadəçi adı və ya şifrə yanlışdır'
      });
    }

    // JWT token yarat
    const token = jwt.sign(
      {
        userId: user.user_id,
        username: user.username,
        role: user.role
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // İstifadəçi məlumatları
    const userResponse = {
      userId: user.user_id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role,
      isActive: user.is_active
    };

    res.json({
      success: true,
      message: 'Uğurla daxil oldunuz',
      token,
      user: userResponse
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server xətası'
    });
  }
});

// Token doğrulama middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Token tapılmadı'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Token etibarsızdır'
      });
    }
    req.user = user;
    next();
  });
};

// Admin yoxlama middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Admin icazəsi tələb olunur'
    });
  }
  next();
};

// Waiter və ya admin yoxlama middleware
const requireWaiterOrAdmin = (req, res, next) => {
  if (!['admin', 'waiter'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Ofisiant və ya admin icazəsi tələb olunur'
    });
  }
  next();
};

// İstifadəçi profili
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId, {
      attributes: ['user_id', 'username', 'email', 'full_name', 'role', 'is_active', 'createdAt']
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'İstifadəçi tapılmadı'
      });
    }

    res.json({
      success: true,
      user: {
        userId: user.user_id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        isActive: user.is_active,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server xətası'
    });
  }
});

module.exports = {
  router,
  authenticateToken,
  requireAdmin,
  requireWaiterOrAdmin
};
