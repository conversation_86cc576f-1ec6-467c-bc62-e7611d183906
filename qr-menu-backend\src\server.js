require('dotenv').config(); // .env faylındakı dəyişənləri yükləyir
const express = require('express');
const http = require('http');
const { Server } = require("socket.io");
const cors = require('cors');

// Verilənlər bazası bağlantısı və modellər
const db = require('./models'); // Modelləri və db.syncDatabase funksiyasını import edir

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
    cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000", // Frontend URL-i .env-dən gəlməlidir
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({ origin: process.env.FRONTEND_URL || "http://localhost:3000" }));
app.use(express.json({ charset: 'utf-8' })); // JSON request body-lərini parse etmək üçün
app.use(express.urlencoded({ extended: true, charset: 'utf-8' })); // URL-encoded request body-lərini parse etmək üçün

// UTF-8 encoding üçün response headers
app.use((req, res, next) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// Əsas Route
app.get('/', (req, res) => {
    res.send('QR Menu Backend API Hazırdır!');
});

// API Routeları
const { router: authRoutes } = require('./routes/auth');
const restaurantConfigRoutes = require('./routes/restaurantConfigRoutes');
const tableRoutes = require('./routes/tableRoutes');
const categoryRoutes = require('./routes/categoryRoutes');
const productRoutes = require('./routes/productRoutes');
const orderRoutes = require('./routes/orderRoutes');
// const reviewRoutes = require('./routes/reviewRoutes');
// const userRoutes = require('./routes/userRoutes');

app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/restaurant-config', restaurantConfigRoutes);
app.use('/api/v1/tables', tableRoutes);
app.use('/api/v1/categories', categoryRoutes);
app.use('/api/v1/products', productRoutes);
app.use('/api/v1/orders', orderRoutes);
// app.use('/api/v1/reviews', reviewRoutes);
// app.use('/api/v1/users', userRoutes);


// Socket.IO bağlantısı
io.on('connection', (socket) => {
    console.log('Bir istifadəçi bağlandı:', socket.id);

    socket.on('disconnect', () => {
        console.log('İstifadəçi ayrıldı:', socket.id);
    });

    // Gələcəkdə sifariş bildirişləri üçün:
    // socket.on('joinAdminRoom', () => {
    //     socket.join('admin_room');
    //     console.log(socket.id, "admin otağına qoşuldu.");
    // });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Serverdə bir xəta baş verdi!');
});

// Verilənlər bazası ilə sinxronizasiya və serveri başlatmaq
// NODE_ENV=development olduqda cədvəlləri yenidən yarada bilər (ehtiyatlı olun!)
const syncOptions = { force: process.env.DB_FORCE_SYNC === 'true' };

db.syncDatabase(syncOptions)
    .then(() => {
        server.listen(PORT, () => {
            console.log(`Backend server ${PORT} portunda işləyir...`);
            if (syncOptions.force) {
                console.warn('DB_FORCE_SYNC aktivdir: Cədvəllər yenidən yaradıldı!');
            }
        });
    })
    .catch(err => {
        console.error('Server başladılarkən xəta (DB sinxronizasiyası və ya başqa):', err);
        // DB olmadan serveri başlatmaq üçün alternativ (əgər istənilirsə)
        // server.listen(PORT, () => {
        //     console.log(`Backend server ${PORT} portunda işləyir... (DB bağlantısı olmadan)`);
        // });
    });

app.set('io', io); // Controller-lərdən io-ya çatmaq üçün
