import { BrowserRouter as Router, Routes, Route, Link, useParams } from 'react-router-dom';
import HomePage from './pages/HomePage';
import CartPage from './pages/CartPage';
import OrderSuccessPage from './pages/OrderSuccessPage';
import { useCart } from './context/CartContext';

const Header = () => {
  const { cartItemCount } = useCart();
  const { tableNumber } = useParams();

  return (
    <header className="bg-light shadow-md sticky top-0 z-10">
      <div className="container mx-auto p-4 flex justify-between items-center">
        <Link to={tableNumber ? `/table/${tableNumber}` : '/'} className="text-2xl font-bold text-secondary">
          QR Menu
        </Link>
        <nav>
          <Link to={tableNumber ? `/table/${tableNumber}/cart` : '/cart'} className="relative text-lg text-primary hover:text-secondary">
            Səbət
            {cartItemCount > 0 && (
              <span className="absolute -top-2 -right-4 bg-secondary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {cartItemCount}
              </span>
            )}
          </Link>
        </nav>
      </div>
    </header>
  );
}

const AppRoutes = () => {
    return (
        <>
            <Header />
            <main className="bg-primary/5 min-h-screen">
                <Routes>
                    <Route path="/" element={<HomePage />} />
                    <Route path="/table/:tableNumber" element={<HomePage />} />
                    <Route path="/cart" element={<CartPage />} />
                    <Route path="/table/:tableNumber/cart" element={<CartPage />} />
                    <Route path="/order-success" element={ <OrderSuccessPage /> } />
                </Routes>
            </main>
        </>
    )
}

function App() {
  return (
    <Router>
        <AppRoutes />
    </Router>
  )
}

export default App;
