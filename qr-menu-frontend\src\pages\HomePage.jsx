import React, { useState, useEffect } from 'react';
import { getCategories, getProducts } from '../services/api';
import ProductList from '../components/ProductList';

const HomePage = () => {
  const [categories, setCategories] = useState([]);
  const [products, setProducts] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [categoriesRes, productsRes] = await Promise.all([
          getCategories(),
          getProducts(),
        ]);
        setCategories(categoriesRes.data);
        setProducts(productsRes.data.products || productsRes.data);
        // Set the first category as default selected
        if (categoriesRes.data.length > 0) {
          setSelectedCategory(categoriesRes.data[0].category_id);
        }
      } catch (err) {
        setError('Məlumatları yükləyərkən xəta baş verdi.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredProducts = selectedCategory
    ? products.filter((product) => product.category_id === selectedCategory)
    : products;

  if (loading) {
    return <div className="text-center p-10">Yüklənir...</div>;
  }

  if (error) {
    return <div className="text-center p-10 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      {/* Category Filter */}
      <nav className="flex justify-center space-x-4 mb-8">
        <button
            onClick={() => setSelectedCategory(null)}
            className={`px-4 py-2 font-semibold rounded-lg transition-colors ${
                !selectedCategory ? 'bg-secondary text-white' : 'bg-light text-primary'
            }`}
        >
            Bütün Məhsullar
        </button>
        {categories.map((category) => (
          <button
            key={category.category_id}
            onClick={() => setSelectedCategory(category.category_id)}
            className={`px-4 py-2 font-semibold rounded-lg transition-colors ${
              selectedCategory === category.category_id
                ? 'bg-secondary text-white'
                : 'bg-light text-primary'
            }`}
          >
            {category.name}
          </button>
        ))}
      </nav>

      {/* Product List */}
      <ProductList products={filteredProducts} />
    </div>
  );
};

export default HomePage;
